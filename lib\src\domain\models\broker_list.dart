import '/src/core/config/app_strings.dart';

import 'agent.dart';

class BrokerList {
  String id;
  String name;
  double totalSales;
  double totalCommission;
  String imageUrl;
  String contact;
  String email;
  String address;
  int agents;
  DateTime joinDate;
  double totalSalesVolume;

  BrokerList({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.contact,
    required this.email,
    required this.address,
    required this.agents,
    required this.totalCommission,
    required this.joinDate, // Add joinDate field
    this.totalSalesVolume = 0.0,
    this.totalSales = 0.0,
  });

  factory BrokerList.fromJson(Map<String, dynamic> json) {
    return BrokerList(
      id: json[brokerListIdKey]?.toString() ?? '',
      name: json[brokerListNameKey]?.toString() ?? '',
      imageUrl: json[brokerListImageUrlKey]?.toString() ?? '',
      contact: json[brokerListContactKey]?.toString() ?? '',
      email: json[brokerListEmailKey]?.toString() ?? '',
      address: json[brokerListAddressKey]?.toString() ?? '',
      agents: _toInt(json[brokerListAgentsKey]),
      totalSales: _toDouble(json[brokerListtotalSalesVolume]),
      totalCommission: _toDouble(json[brokerListTotalCommissionKey]),
      joinDate: _parseDate(json[brokerListJoinDateKey]),
      totalSalesVolume: _toDouble(json['totalSalesVolume']),
    );
  }

  // Helper method for safe int conversion
  static int _toInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  // Helper method for safe double conversion
  static double _toDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  // Helper method for safe DateTime parsing
  static DateTime _parseDate(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is String && value.isNotEmpty) {
      return DateTime.tryParse(value) ?? DateTime.now();
    }
    return DateTime.now();
  }
}
