class TokenStorage {
  static TokenStorage? _instance;
  static TokenStorage get instance => _instance ??= TokenStorage._();

  TokenStorage._();

  //TODO: Remove hardcoded token
  final token =
      'eyJhbGciOiJIUzUxMiJ9.********************************************************************************************************.wBhsn8vis-q8v4WD5ohRtUVI1MpMdwxQqG-W1pvEyk02NocJXFgY-WdRzjbeZwByZ-40rnEVTJL-1H6ROSCvlw';

  Future<String?> getToken() async {
    return token;
  }
}
