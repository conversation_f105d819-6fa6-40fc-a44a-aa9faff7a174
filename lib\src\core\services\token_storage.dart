class TokenStorage {
  static TokenStorage? _instance;
  static TokenStorage get instance => _instance ??= TokenStorage._();

  TokenStorage._();

  //TODO: Remove hardcoded token
  final token =
      'eyJhbGciOiJIUzUxMiJ9.********************************************************************************************************.6kVu347s1iixZMzkuiaY6lB7BG8SUr_CJOULvq53tiyBc9JnxYga3Ltmbn5Ypb6QpKac4LwlnXOhX5Dy1wv71g';

  Future<String?> getToken() async {
    return token;
  }
}
