import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '/src/core/config/app_strings.dart';
import '/src/core/config/constants.dart';
import '/src/core/config/responsive.dart';
import '/src/core/theme/app_theme.dart';
import 'dart:math' as math;
import '../../../../core/theme/app_fonts.dart';

class TableCellData {
  final String text;
  final Widget? widget;
  final TextAlign? alignment;
  final IconData? leftIcon;
  final IconData? rightIcon;
  final String? leftIconAsset;
  final String? rightIconAsset;
  final Color? iconColor;
  final double? iconSize;

  TableCellData({
    required this.text, 
    this.widget, 
    this.alignment,
    this.leftIcon,
    this.rightIcon,
    this.leftIconAsset,
    this.rightIconAsset,
    this.iconColor,
    this.iconSize = 16,
  });
}

class CustomDataTableWidget<T> extends HookWidget {
  final List<T> data;
  final String? title;
  final String? titleIcon;
  final String? searchHint;
  final String? titleFilterLabel;

  // Legacy filter system (for backward compatibility)
  final List<String> filter1Options;
  final List<String> filter2Options;
  final List<String> filter3Options;
  final String? filter1Label;
  final String? filter2Label;
  final String? filter3Label;
  final bool Function(T item, String?, String?, String?)? filterFn;

  // Dynamic filter configuration (new system)
  final List<String> filterColumnNames; // Column names to create filters for
  final Map<String, String Function(T)>?
  filterValueExtractors; // How to extract values for each filter column

  final String Function(T item)? searchFn;
  final List<String> columnNames;
  final List<String Function(T)> cellBuilders;
  final List<Widget Function(BuildContext, T)?>?
  widgetCellBuilders; // Optional widget builders for specific columns (can contain null)
  final List<bool>?
  useWidgetBuilders; // Boolean flags to indicate which columns should use widget builders
  final List<Widget Function(BuildContext, T)>? actionBuilders;
  final Widget Function(BuildContext, T)? mobileCardBuilder;
  final void Function(String columnName, bool ascending)? onSort;

  // New properties for icon support
  final List<TableCellData Function(T)?>? iconCellBuilders; // Returns TableCellData with icon info
  final List<bool>? useIconBuilders; // Boolean flags for which columns use icon builders

  // Empty state customization
  final String? emptyStateMessage;

  // Min height constraint
  final bool useMinHeight;
  final double? minHeight;

  const CustomDataTableWidget({
    super.key,
    required this.data,
    this.title,
    this.titleIcon,
    this.searchHint,
    this.searchFn,
    required this.columnNames,
    required this.cellBuilders,
    this.widgetCellBuilders,
    this.useWidgetBuilders,
    this.iconCellBuilders,
    this.useIconBuilders,
    this.actionBuilders,
    this.mobileCardBuilder,
    this.onSort,
    this.titleFilterLabel,
    // Legacy filter system
    this.filter1Options = const [],
    this.filter2Options = const [],
    this.filter3Options = const [],
    this.filter1Label,
    this.filter2Label,
    this.filter3Label,
    this.filterFn,
    // Dynamic filter system
    this.filterColumnNames = const [],
    this.filterValueExtractors,
    this.emptyStateMessage,
    // Min height constraint
    this.useMinHeight = false,
    this.minHeight,
  });

  @override
  Widget build(BuildContext context) {
    final currentPage = useState(1);
    final itemsPerPage = useState(10);
    final selectedF1 = useState<String?>(null);
    final selectedF2 = useState<String?>(null);
    final selectedF3 = useState<String?>(null);
    final searchQuery = useState('');
    final showFilter = useState(false);
    final showTooltip = useState(false);
    final appliedF1 = useState<String?>(null);
    final appliedF2 = useState<String?>(null);
    final appliedF3 = useState<String?>(null);
    final sortColumn = useState<String>('');
    final sortAscending = useState<bool>(true);

    // Dynamic filter states
    final appliedFilters = useState<Map<String, String?>>({});
    final selectedFilters = useState<Map<String, String?>>({});

    void handleSort(String columnName) {
      if (onSort != null) {
        if (sortColumn.value == columnName) {
          sortAscending.value = !sortAscending.value;
        } else {
          sortColumn.value = columnName;
          sortAscending.value = true;
        }
        onSort!(columnName, sortAscending.value);
      }
    }

    List<T> getFilteredItems() {
      var filtered = data;

      if (searchQuery.value.isNotEmpty && searchFn != null) {
        filtered = filtered
            .where(
              (item) => searchFn!(
                item,
              ).toLowerCase().contains(searchQuery.value.toLowerCase()),
            )
            .toList();
      }

      // Apply legacy filters if filterFn is provided
      if (filterFn != null) {
        filtered = filtered
            .where(
              (item) => filterFn!(
                item,
                filter1Options.isNotEmpty ? appliedF1.value : null,
                filter2Options.isNotEmpty ? appliedF2.value : null,
                filter3Options.isNotEmpty ? appliedF3.value : null,
              ),
            )
            .toList();
      }

      // Apply dynamic filters if configured - only use applied values, not selected
      if (filterColumnNames.isNotEmpty && filterValueExtractors != null) {
        for (String columnName in filterColumnNames) {
          final appliedValue = appliedFilters.value[columnName];
          if (appliedValue != null && appliedValue.isNotEmpty) {
            final extractor = filterValueExtractors![columnName];
            if (extractor != null) {
              filtered = filtered
                  .where((item) => extractor(item) == appliedValue)
                  .toList();
            }
          }
        }
      }
      return filtered;
    }

    List<T> getPaginatedItems() {
      final filtered = getFilteredItems();

      // Safety check: if current page is beyond available pages, reset to page 1
      final totalPages = (filtered.length / itemsPerPage.value).ceil();
      if (currentPage.value > totalPages && totalPages > 0) {
        currentPage.value = 1;
      }

      final startIndex = (currentPage.value - 1) * itemsPerPage.value;
      final endIndex = (startIndex + itemsPerPage.value).clamp(
        0,
        filtered.length,
      );

      // Additional safety check for sublist bounds
      if (startIndex >= filtered.length) {
        return <T>[];
      }

      return filtered.sublist(startIndex, endIndex);
    }

    int getTotalPages() =>
        (getFilteredItems().length / itemsPerPage.value).ceil();

    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          width: constraints.maxWidth,
          constraints: useMinHeight && minHeight != null 
              ? BoxConstraints(minHeight: minHeight!)
              : null,
          padding: const EdgeInsets.only(bottom: defaultPadding - 2),
          decoration: BoxDecoration(
            color: AppTheme.white,
            borderRadius: BorderRadius.circular(15),
            boxShadow: [
              BoxShadow(
                color: AppTheme.black.withValues(alpha: 0.05),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(15),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (showFilter.value)
                  _buildFilterWidget(
                    context,
                    selectedF1,
                    selectedF2,
                    selectedF3,
                    showFilter,
                    currentPage,
                    showTooltip,
                    appliedF1,
                    appliedF2,
                    appliedF3,
                    appliedFilters,
                    selectedFilters,
                  ),
                // Only show header if title is provided or filters/search are available
                if (title != null || 
                    filterColumnNames.isNotEmpty || 
                    filter1Options.isNotEmpty || 
                    filter2Options.isNotEmpty || 
                    filter3Options.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.fromLTRB(
                      defaultPadding * 1.5,
                      defaultPadding - 2,
                      defaultPadding * 1.5,
                      0,
                    ),
                    child: _buildHeader(
                      context,
                      searchQuery,
                      showFilter,
                      showTooltip,
                      appliedF1,
                      appliedF2,
                      appliedF3,
                      appliedFilters,
                      selectedF1,
                      selectedF2,
                      selectedF3,
                      selectedFilters,
                    ),
                  ),
                if (title != null ||
                    filterColumnNames.isNotEmpty ||
                    filter1Options.isNotEmpty ||
                    filter2Options.isNotEmpty ||
                    filter3Options.isNotEmpty)
                  const SizedBox(height: 4),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: defaultPadding * 1.5,
                  ),
                  child: _buildTable(
                    context,
                    getPaginatedItems(),
                    handleSort,
                    sortColumn,
                    sortAscending,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(
                    defaultPadding * 1.5,
                    defaultPadding,
                    defaultPadding * 1.5,
                    defaultPadding * 1.5,
                  ),
                  child: _buildPagination(
                    context,
                    getFilteredItems(),
                    currentPage,
                    itemsPerPage,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFilterWidget(
    BuildContext context,
    ValueNotifier<String?> selectedFilter1,
    ValueNotifier<String?> selectedFilter2,
    ValueNotifier<String?> selectedFilter3,
    ValueNotifier<bool> showFilter,
    ValueNotifier<int> currentPage,
    ValueNotifier<bool> showTooltip,
    ValueNotifier<String?> appliedFilter1,
    ValueNotifier<String?> appliedFilter2,
    ValueNotifier<String?> appliedFilter3,
    ValueNotifier<Map<String, String?>> appliedFilters,
    ValueNotifier<Map<String, String?>> selectedFilters,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(color: AppTheme.filterBgColor),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(filterBy, style: AppFonts.semiBoldTextStyle(18)),
              GestureDetector(
                onTap: () {
                  showFilter.value = false;
                },
                child: const Icon(Icons.close, size: 20),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Make filter controls responsive with proper wrapping
          LayoutBuilder(
            builder: (context, constraints) {
              final isSmallScreen = constraints.maxWidth < 600;

              // Build dynamic filters
              List<Widget> filterWidgets = [];

              // Add legacy filters if they exist
              if (filter1Options.isNotEmpty && filter1Label != null) {
                filterWidgets.add(
                  _buildDropdown(
                    filter1Label!,
                    selectedFilter1.value,
                    filter1Options,
                    (value) => selectedFilter1.value = value,
                  ),
                );
              }
              if (filter2Options.isNotEmpty && filter2Label != null) {
                filterWidgets.add(
                  _buildDropdown(
                    filter2Label!,
                    selectedFilter2.value,
                    filter2Options,
                    (value) => selectedFilter2.value = value,
                  ),
                );
              }
              if (filter3Options.isNotEmpty && filter3Label != null) {
                filterWidgets.add(
                  _buildDropdown(
                    filter3Label!,
                    selectedFilter3.value,
                    filter3Options,
                    (value) => selectedFilter3.value = value,
                  ),
                );
              }

              // Add dynamic filters
              for (String columnName in filterColumnNames) {
                final options = _getDynamicFilterOptions(columnName);
                if (options.isNotEmpty) {
                  filterWidgets.add(
                    _buildDropdown(
                      _getFilterLabel(columnName),
                      selectedFilters.value[columnName],
                      options,
                      (value) {
                        final newFilters = Map<String, String?>.from(
                          selectedFilters.value,
                        );
                        newFilters[columnName] = value;
                        selectedFilters.value = newFilters;
                      },
                    ),
                  );
                }
              }

              if (isSmallScreen) {
                // Stack filters vertically on small screens
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    for (int i = 0; i < filterWidgets.length; i++) ...[
                      SizedBox(width: double.infinity, child: filterWidgets[i]),
                      if (i < filterWidgets.length - 1)
                        const SizedBox(height: 12),
                    ],
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      height: 40,
                      child: ElevatedButton(
                        onPressed: () {
                          // Apply legacy filters
                          appliedFilter1.value = selectedFilter1.value;
                          appliedFilter2.value = selectedFilter2.value;
                          appliedFilter3.value = selectedFilter3.value;
                          // Apply dynamic filters - copy selected to applied
                          appliedFilters.value = Map<String, String?>.from(selectedFilters.value);
                          currentPage.value = 1;
                          showTooltip.value = true;
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.roundIconColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ),
                        child: const Text(apply),
                      ),
                    ),
                  ],
                );
              } else {
                // Use horizontal layout for larger screens with proper wrapping
                return Wrap(
                  spacing: 12,
                  runSpacing: 12,
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    // Add all filter widgets (legacy + dynamic)
                    for (Widget filterWidget in filterWidgets)
                      SizedBox(
                        width: ResponsiveSizes.comboBoxWidth(context),
                        child: filterWidget,
                      ),
                    SizedBox(
                      width: ResponsiveSizes.applyButtonWidth(context),
                      height: 40,
                      child: ElevatedButton(
                        onPressed: () {
                          // Apply legacy filters
                          appliedFilter1.value = selectedFilter1.value;
                          appliedFilter2.value = selectedFilter2.value;
                          appliedFilter3.value = selectedFilter3.value;
                          // Apply dynamic filters - copy selected to applied
                          appliedFilters.value = Map<String, String?>.from(selectedFilters.value);
                          currentPage.value = 1;
                          showTooltip.value = true;
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.roundIconColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ),
                        child: const Text(apply),
                      ),
                    ),
                  ],
                );
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDropdown(
    String hint,
    String? value,
    List<String> items,
    Function(String?) onChanged,
  ) {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: value != null
              ? AppTheme.selectedComboBoxBorder
              : AppTheme.comboBoxBorder,
          width: 1.0,
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: ButtonTheme(
          alignedDropdown: true,
          child: DropdownButton<String>(
            value: value,
            hint: Padding(
              padding: const EdgeInsets.only(left: 12),
              child: Text(
                hint,
                style: AppFonts.regularTextStyle(14, color: AppTheme.black),
              ),
            ),
            isExpanded: true,
            dropdownColor: Colors.white,
            menuMaxHeight: 300,
            borderRadius: BorderRadius.circular(20),
            items: items.map((item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Padding(
                  padding: const EdgeInsets.only(left: 12),
                  child: Text(item, style: AppFonts.regularTextStyle(14)),
                ),
              );
            }).toList(),
            onChanged: onChanged,
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(
    BuildContext context,
    ValueNotifier<String> searchQuery,
    ValueNotifier<bool> showFilter,
    ValueNotifier<bool> showTooltip,
    ValueNotifier<String?> appliedF1,
    ValueNotifier<String?> appliedF2,
    ValueNotifier<String?> appliedF3,
    ValueNotifier<Map<String, String?>> appliedFilters,
    ValueNotifier<String?> selectedF1,
    ValueNotifier<String?> selectedF2,
    ValueNotifier<String?> selectedF3,
    ValueNotifier<Map<String, String?>> selectedFilters,
  ) {
    // Check if we have any filters or search functionality
    final hasFilters = filterColumnNames.isNotEmpty || 
                      filter1Options.isNotEmpty || 
                      filter2Options.isNotEmpty || 
                      filter3Options.isNotEmpty;
    
    if (Responsive.isMobile(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    if (titleIcon != null) ...[
                      SizedBox(width: 20, height: 20, child: Image.asset(titleIcon!)),
                      const SizedBox(width: 8),
                    ],
                    Text(title!, style: AppFonts.semiBoldTextStyle(18)),
                    const SizedBox(width: 8),
                    // Show filter results banner inline with title on mobile only (not tablet)
                    if (!Responsive.isTablet(context) && showTooltip.value && _hasAppliedFilters(appliedF1, appliedF2, appliedF3, appliedFilters))
                      Expanded(
                        child: _buildFilterResultsBanner(
                          context,
                          appliedF1,
                          appliedF2,
                          appliedF3,
                          appliedFilters,
                          showTooltip,
                          selectedF1,
                          selectedF2,
                          selectedF3,
                          selectedFilters,
                        ),
                      ),
                    if (!showTooltip.value || !_hasAppliedFilters(appliedF1, appliedF2, appliedF3, appliedFilters) || Responsive.isTablet(context))
                      const Spacer(),
                  ],
                ),
                // Show filter results banner on next line for tablet
                if (Responsive.isTablet(context) && showTooltip.value && _hasAppliedFilters(appliedF1, appliedF2, appliedF3, appliedFilters))
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: _buildFilterResultsBanner(
                      context,
                      appliedF1,
                      appliedF2,
                      appliedF3,
                      appliedFilters,
                      showTooltip,
                      selectedF1,
                      selectedF2,
                      selectedF3,
                      selectedFilters,
                    ),
                  ),
              ],
            ),
          if (title != null && hasFilters) const SizedBox(height: 12),
          if (hasFilters)
            Row(
              children: [
                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () {
                      showFilter.value = !showFilter.value;
                      // if (showFilter.value) {
                      //   // When opening filter, restore previously selected values
                      //   // selectedF1.value = appliedF1.value;
                      //   // selectedF2.value = appliedF2.value;
                      //   // selectedF3.value = appliedF3.value;
                      // }
                    },
                    child: Container(
                      constraints: BoxConstraints(
                        minWidth: ResponsiveSizes.filterButtonWidth(context),
                      ),
                      height: 40,
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        color: showFilter.value
                            ? AppTheme.selectedComboBoxBorder
                            : AppTheme.searchbarBg,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: Image.asset(
                              '$iconAssetpath/filter.png',
                              color: showFilter.value
                                  ? Colors.white
                                  : AppTheme.tableDataFont,
                            ),
                          ),
                          const SizedBox(width: 6),
                          Text(
                            "Filter",
                            style: AppFonts.regularTextStyle(
                              14,
                              color: showFilter.value
                                  ? Colors.white
                                  : AppTheme.tableDataFont,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: SizedBox(
                    height: 40,
                    child: TextField(
                      onChanged: (value) => searchQuery.value = value,
                      decoration: InputDecoration(
                        hintText: searchHint ?? 'Search',
                        hintStyle: AppFonts.regularTextStyle(
                          14,
                          color: AppTheme.tableDataFont,
                        ),
                        prefixIcon: Container(
                          height: 24,
                          width: 24,
                          padding: const EdgeInsets.only(
                            left: 8,
                            top: 8,
                            bottom: 8,
                          ),
                          child: Image.asset('$iconAssetpath/search.png'),
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(20),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: AppTheme.searchbarBg,
                        contentPadding: const EdgeInsets.symmetric(vertical: 0),
                      ),
                    ),
                  ),
                ),
              ],
            ),
        ],
      );
    }
    // For larger screens (tablet and desktop)
    if (Responsive.isTablet(context)) {
      // Tablet layout: banner on next line
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Row(
                children: [
                  if (titleIcon != null) ...[
                    SizedBox(width: 20, height: 20, child: Image.asset(titleIcon!)),
                    const SizedBox(width: 8),
                  ],
                  Text(title ?? '', style: AppFonts.semiBoldTextStyle(22)),
                ],
              ),
              const Spacer(),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                    if (hasFilters) ...[
                      MouseRegion(
                        cursor: SystemMouseCursors.click,
                        child: GestureDetector(
                          onTap: () {
                            showFilter.value = !showFilter.value;
                          },
                          child: Container(
                            constraints: BoxConstraints(
                              minWidth: ResponsiveSizes.filterButtonWidth(context),
                            ),
                            height: 40,
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            decoration: BoxDecoration(
                              color: showFilter.value
                                  ? AppTheme.selectedComboBoxBorder
                                  : AppTheme.searchbarBg,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: Image.asset(
                                    '$iconAssetpath/filter.png',
                                    color: showFilter.value
                                        ? Colors.white
                                        : AppTheme.tableDataFont,
                                  ),
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  "Filter",
                                  style: AppFonts.regularTextStyle(
                                    14,
                                    color: showFilter.value
                                        ? Colors.white
                                        : AppTheme.tableDataFont,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: SizedBox(
                          height: 40,
                          child: TextField(
                            onChanged: (value) => searchQuery.value = value,
                            decoration: InputDecoration(
                              hintText: searchHint ?? 'Search',
                              hintStyle: AppFonts.regularTextStyle(
                                14,
                                color: AppTheme.tableDataFont,
                              ),
                              prefixIcon: Container(
                                height: 24,
                                width: 24,
                                padding: const EdgeInsets.only(
                                  left: 8,
                                  top: 8,
                                  bottom: 8,
                                ),
                                child: Image.asset('$iconAssetpath/search.png'),
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(20),
                                borderSide: BorderSide.none,
                              ),
                              filled: true,
                              fillColor: AppTheme.searchbarBg,
                              contentPadding: const EdgeInsets.symmetric(vertical: 0),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
            ],
          ),
          // Show filter results banner on next line for tablet
          if (showTooltip.value && _hasAppliedFilters(appliedF1, appliedF2, appliedF3, appliedFilters))
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: _buildFilterResultsBanner(
                context,
                appliedF1,
                appliedF2,
                appliedF3,
                appliedFilters,
                showTooltip,
                selectedF1,
                selectedF2,
                selectedF3,
                selectedFilters,
              ),
            ),
        ],
      );
    }

    // For desktop screens, use the original row layout
    return Row(
      children: [
        Row(
          children: [
            if (titleIcon != null) ...[
              SizedBox(width: 20, height: 20, child: Image.asset(titleIcon!)),
              const SizedBox(width: 8),
            ],
            Text(title ?? '', style: AppFonts.semiBoldTextStyle(22)),
            const SizedBox(width: 12),
            // Show filter results banner inline with title on desktop
            if (showTooltip.value && _hasAppliedFilters(appliedF1, appliedF2, appliedF3, appliedFilters))
              _buildFilterResultsBanner(
                context,
                appliedF1,
                appliedF2,
                appliedF3,
                appliedFilters,
                showTooltip,
                selectedF1,
                selectedF2,
                selectedF3,
                selectedFilters,
              ),
          ],
        ),
        const Spacer(),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: () => showFilter.value = !showFilter.value,
                  child: Container(
                    constraints: BoxConstraints(
                      minWidth: ResponsiveSizes.filterButtonWidth(context),
                    ),
                    height: 40,
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      color: showFilter.value
                          ? AppTheme.selectedComboBoxBorder
                          : AppTheme.searchbarBg,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: Image.asset(
                            '$iconAssetpath/filter.png',
                            color: showFilter.value
                                ? Colors.white
                                : AppTheme.tableDataFont,
                          ),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          "Filter",
                          style: AppFonts.regularTextStyle(
                            14,
                            color: showFilter.value
                                ? Colors.white
                                : AppTheme.tableDataFont,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: SizedBox(
                  height: 40,
                  child: TextField(
                    onChanged: (value) => searchQuery.value = value,
                    decoration: InputDecoration(
                      hintText: searchHint ?? 'Search',
                      hintStyle: AppFonts.regularTextStyle(
                        14,
                        color: AppTheme.tableDataFont,
                      ),
                      prefixIcon: Container(
                        height: 24,
                        width: 24,
                        padding: const EdgeInsets.only(
                          left: 8,
                          top: 8,
                          bottom: 8,
                        ),
                        child: Image.asset('$iconAssetpath/search.png'),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: AppTheme.searchbarBg,
                      contentPadding: const EdgeInsets.symmetric(vertical: 0),
                    ),
                  ),
                ),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildTable(
    BuildContext context,
    List<T> items,
    void Function(String) handleSort,
    ValueNotifier<String> sortColumn,
    ValueNotifier<bool> sortAscending,
  ) {
    // Show empty state if no items
    if (items.isEmpty) {
      return _buildEmptyState(context);
    }

    if (Responsive.isMobile(context)) {
      return _buildMobileView(context, items);
    } else if (Responsive.isTablet(context)) {
      return _buildTabletTable(
        context,
        items,
        handleSort,
        sortColumn,
        sortAscending,
      );
    } else {
      return _buildDesktopTable(
        context,
        items,
        handleSort,
        sortColumn,
        sortAscending,
      );
    }
  }

  Widget _buildDesktopTable(
    BuildContext context,
    List<T> items,
    void Function(String) handleSort,
    ValueNotifier<String> sortColumn,
    ValueNotifier<bool> sortAscending,
  ) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final ScrollController horizontalScrollController = ScrollController();

        final columns = columnNames
            .map(
              (name) => _dataColumn(
                name: name,
                allowSort: true,
                handleSort: handleSort,
                sortColumn: sortColumn.value,
                sortAscending: sortAscending.value,
              ),
            )
            .toList();

        if (actionBuilders != null && actionBuilders!.isNotEmpty) {
          columns.add(
            _dataColumn(
              name: 'Actions',
              allowSort: false,
              handleSort: handleSort,
              sortColumn: sortColumn.value,
              sortAscending: sortAscending.value,
            ),
          );
        }

        // Calculate minimum required width based on number of columns
        final int totalColumns =
            columnNames.length +
            (actionBuilders != null && actionBuilders!.isNotEmpty ? 1 : 0);
        final double minRequiredWidth =
            totalColumns * 120.0; // Minimum 120px per column
        final bool enableScroll = constraints.maxWidth < minRequiredWidth;

        Widget tableContent = SizedBox(
          width: enableScroll
              ? math.max(minRequiredWidth, 1200)
              : constraints.maxWidth,
          child: DataTable(
            columnSpacing: defaultPadding * 0.8,
            dataRowMinHeight: 40,
            dataRowMaxHeight: 50,
            horizontalMargin: 0,
            checkboxHorizontalMargin: 0,
            columns: columns,
            rows: items.map((item) => _buildDataRow(context, item)).toList(),
          ),
        );

        if (enableScroll) {
          return Scrollbar(
            controller: horizontalScrollController,
            thumbVisibility: true,
            child: SingleChildScrollView(
              controller: horizontalScrollController,
              scrollDirection: Axis.horizontal,
              child: tableContent,
            ),
          );
        } else {
          return tableContent;
        }
      },
    );
  }

  Widget _buildTabletTable(
    BuildContext context,
    List<T> items,
    void Function(String) handleSort,
    ValueNotifier<String> sortColumn,
    ValueNotifier<bool> sortAscending,
  ) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final ScrollController horizontalScrollController = ScrollController();

        final columns = columnNames
            .map(
              (name) => _dataColumn(
                name: name,
                allowSort: true,
                handleSort: handleSort,
                sortColumn: sortColumn.value,
                sortAscending: sortAscending.value,
                fontSize: 12,
              ),
            )
            .toList();

        if (actionBuilders != null && actionBuilders!.isNotEmpty) {
          columns.add(
            _dataColumn(
              name: 'Actions',
              allowSort: false,
              handleSort: handleSort,
              sortColumn: sortColumn.value,
              sortAscending: sortAscending.value,
              fontSize: 12,
            ),
          );
        }

        // Calculate minimum required width for tablet based on number of columns
        final int totalColumns =
            columnNames.length +
            (actionBuilders != null && actionBuilders!.isNotEmpty ? 1 : 0);
        final double minRequiredWidth =
            totalColumns * 100.0; // Minimum 100px per column for tablet

        return Scrollbar(
          controller: horizontalScrollController,
          thumbVisibility: true, // Always show scrollbar for tablet
          child: SingleChildScrollView(
            controller: horizontalScrollController,
            scrollDirection: Axis.horizontal,
            child: SizedBox(
              width: math.max(
                minRequiredWidth,
                math.max(constraints.maxWidth, 800),
              ),
              child: DataTable(
                columnSpacing: defaultPadding * 0.6,
                dataRowMinHeight: 48,
                dataRowMaxHeight: 52,
                horizontalMargin: 0,
                checkboxHorizontalMargin: 0,
                columns: columns,
                rows: items
                    .map((item) => _buildDataRow(context, item))
                    .toList(),
              ),
            ),
          ),
        );
      },
    );
  }

  DataColumn _dataColumn({
    required String name,
    bool allowSort = true,
    required void Function(String) handleSort,
    required String sortColumn,
    required bool sortAscending,
    double fontSize = 14,
  }) {
   return DataColumn(
    label: Builder(
      builder: (context) {
        final processedName = (Responsive.isTablet(context))
            ? name.replaceAll(' ', '\n')
            : name;

        return Expanded(
          child: GestureDetector(
            onTap: allowSort && name != 'Actions' ? () => handleSort(name) : null,
            child: Container(
              alignment: Alignment.centerLeft,
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: IntrinsicWidth(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Flexible(
                      child: Text(
                        processedName,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.left,
                        softWrap: true,
                        style: AppFonts.regularTextStyle(
                          fontSize,
                          color: AppTheme.tableHeaderFont,
                        ),
                      ),
                    ),
                    if (allowSort && name != 'Actions') ...[
                      const SizedBox(width: 4),
                      Image.asset(
                        '$iconAssetpath/column_sort.png',
                        height: 14,
                        width: 14,
                        color: sortColumn == name
                            ? AppTheme.primaryColor
                            : AppTheme.tableHeaderFont,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    ),
  );
}

  DataRow _buildDataRow(BuildContext context, T item) {
    final cells = <DataCell>[];

    for (int i = 0; i < cellBuilders.length; i++) {
      // Check if this column should use icon builder
      final bool useIcon = useIconBuilders != null &&
          i < useIconBuilders!.length &&
          useIconBuilders![i] &&
          iconCellBuilders != null &&
          i < iconCellBuilders!.length &&
          iconCellBuilders![i] != null;

      // Check if this column should use widget builder
      final bool useWidget = useWidgetBuilders != null &&
          i < useWidgetBuilders!.length &&
          useWidgetBuilders![i] &&
          widgetCellBuilders != null &&
          i < widgetCellBuilders!.length &&
          widgetCellBuilders![i] != null;

      cells.add(
        DataCell(
          Container(
            alignment: Alignment.centerLeft,
            child: useWidget
                ? widgetCellBuilders![i]!(context, item)
                : useIcon
                    ? _buildIconCell(iconCellBuilders![i]!(item))
                    : Text(
                        cellBuilders[i](item),
                        style: AppFonts.regularTextStyle(
                          Responsive.isTablet(context) ? 12 : 14
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
          ),
        ),
      );
    }

    if (actionBuilders != null && actionBuilders!.isNotEmpty) {
      cells.add(
        DataCell(
          Container(
            width: ResponsiveSizes.actionColumnWidth(context),
            alignment: Alignment.center,
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: actionBuilders!
                  .map((builder) => builder(context, item))
                  .toList(),
            ),
          ),
        ),
      );
    }

    return DataRow(cells: cells);
  }

  Widget _buildIconCell(TableCellData cellData) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (cellData.leftIcon != null)
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: Icon(
              cellData.leftIcon,
              size: cellData.iconSize,
              color: cellData.iconColor,
            ),
          ),
        if (cellData.leftIconAsset != null)
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: Image.asset(
              cellData.leftIconAsset!,
              width: cellData.iconSize,
              height: cellData.iconSize,
              color: cellData.iconColor,
            ),
          ),
        Flexible(
          child: Text(
            cellData.text,
            style: AppFonts.regularTextStyle(14),
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
            textAlign: cellData.alignment ?? TextAlign.left,
          ),
        ),
        if (cellData.rightIcon != null)
          Padding(
            padding: const EdgeInsets.only(left: 8),
            child: Icon(
              cellData.rightIcon,
              size: cellData.iconSize,
              color: cellData.iconColor,
            ),
          ),
        if (cellData.rightIconAsset != null)
          Padding(
            padding: const EdgeInsets.only(left: 8),
            child: Image.asset(
              cellData.rightIconAsset!,
              width: cellData.iconSize,
              height: cellData.iconSize,
              color: cellData.iconColor,
            ),
          ),
      ],
    );
  }

  Widget _buildMobileView(BuildContext context, List<T> items) {
    if (mobileCardBuilder == null) {
      return const SizedBox();
    }

    return Column(
      children: items.map((item) => mobileCardBuilder!(context, item)).toList(),
    );
  }

  Widget _buildPagination(
    BuildContext context,
    List<T> filteredItems,
    ValueNotifier<int> currentPage,
    ValueNotifier<int> itemsPerPage,
  ) {
    // Don't show pagination if no data
    if (filteredItems.isEmpty) {
      return const SizedBox.shrink();
    }

    final totalPages = (filteredItems.length / itemsPerPage.value).ceil();

    // Always show pagination info, even with 1 page
    if (Responsive.isMobile(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _showingDataText(
            context,
            currentPage.value,
            itemsPerPage.value,
            filteredItems,
          ),
          if (totalPages > 1) ...[
            const SizedBox(height: defaultPadding),
            _buildPaginationControls(context, currentPage, totalPages),
          ],
        ],
      );
    }

    return Row(
      children: [
        _showingDataText(
          context,
          currentPage.value,
          itemsPerPage.value,
          filteredItems,
        ),
        if (totalPages > 0) ...[
          const Spacer(),
          _buildPaginationControls(context, currentPage, totalPages),
        ],
      ],
    );
  }

  Widget _showingDataText(
    BuildContext context,
    int currentPage,
    int itemsPerPage,
    List<T> filteredItems,
  ) {
    final startIndex = (currentPage - 1) * itemsPerPage + 1;
    final endIndex = (currentPage * itemsPerPage).clamp(
      0,
      filteredItems.length,
    );

    return Text(
      'Showing $startIndex to $endIndex of ${filteredItems.length} entries',
      style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
    );
  }

  Widget _buildPaginationControls(
    BuildContext context,
    ValueNotifier<int> currentPage,
    int totalPages,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _paginationButton(
          icon: Icons.chevron_left,
          onPressed: currentPage.value > 1 ? () => currentPage.value-- : null,
        ),
        ...List.generate(math.min(5, totalPages), (index) {
          final pageNum = index + 1;
          return _paginationButton(
            label: pageNum.toString(),
            isSelected: pageNum == currentPage.value,
            onPressed: () => currentPage.value = pageNum,
          );
        }),
        if (totalPages > 5) ...[
          _paginationButton(label: '...'),
          _paginationButton(
            label: totalPages.toString(),
            onPressed: () => currentPage.value = totalPages,
          ),
        ],
        _paginationButton(
          icon: Icons.chevron_right,
          onPressed: currentPage.value < totalPages
              ? () => currentPage.value++
              : null,
        ),
      ],
    );
  }

  Widget _paginationButton({
    String? label,
    IconData? icon,
    bool isSelected = false,
    VoidCallback? onPressed,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 2),
      child: Material(
        color: isSelected ? AppTheme.paginationActiveBg : Colors.transparent,
        borderRadius: BorderRadius.circular(5),
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(5),
          child: Container(
            width: 30,
            height: 30,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Center(
              child: icon != null
                  ? Icon(
                      icon,
                      size: 16,
                      color: isSelected ? Colors.white : Colors.black,
                    )
                  : Text(
                      label!,
                      style: AppFonts.regularTextStyle(
                        12,
                        color: isSelected ? Colors.white : Colors.black,
                      ),
                    ),
            ),
          ),
        ),
      ),
    );
  }


Widget buildFilterResultBanner({
  required String showingResultsFor,
  required String date,
  required VoidCallback onClear,
}) {
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 8),
    height: 20,
    decoration: BoxDecoration(
      color: AppTheme.filterResultBannerColor,
      borderRadius: BorderRadius.circular(25),
      border: Border.all(
        color: AppTheme.filterResultBannerBorderColor,
        width: 0.5,
      ),
    ),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        RichText(
          text: TextSpan(
            text: showingResultsFor,
            style: AppFonts.regularTextStyle(10, color: AppTheme.black),
            children: [
              TextSpan(
                text: date,
                style: AppFonts.mediumTextStyle(11, color: AppTheme.black),
              ),
            ],
          ),
        ),
        const SizedBox(width: 5),
        SizedBox(
          width: 10,
          child: IconButton(
            padding: EdgeInsets.zero,
            alignment: Alignment.center,
            icon: const Icon(Icons.close, size: 9),
            style: IconButton.styleFrom(backgroundColor: Colors.transparent),
            onPressed: onClear,
          ),
        ),
      ],
    ),
  );
}
  Widget _buildEmptyState(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(defaultPadding*3),
      child: Center(
        child: Text(
          emptyStateMessage ?? noDataAvailable,
          style: AppFonts.regularTextStyle(
            Responsive.isMobile(context) ? 16 : 18,
            color: AppTheme.primaryTextColor.withValues(alpha: 0.6),
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  // Helper method to generate dynamic filter options
  List<String> _getDynamicFilterOptions(String columnName) {
    if (filterValueExtractors == null ||
        !filterValueExtractors!.containsKey(columnName)) {
      return [];
    }

    final extractor = filterValueExtractors![columnName]!;
    final uniqueValues = data.map(extractor).toSet().toList();
    uniqueValues.sort(); // Sort alphabetically
    return uniqueValues;
  }

  // Helper method to get filter label (use column name if not specified)
  String _getFilterLabel(String columnName) {
    return columnName; // You can customize this logic if needed
  }

  // Helper method to check if any filters are applied
  bool _hasAppliedFilters(
    ValueNotifier<String?> appliedF1,
    ValueNotifier<String?> appliedF2,
    ValueNotifier<String?> appliedF3,
    ValueNotifier<Map<String, String?>> appliedFilters,
  ) {
    // Check legacy filters
    if (appliedF1.value != null && appliedF1.value!.isNotEmpty) return true;
    if (appliedF2.value != null && appliedF2.value!.isNotEmpty) return true;
    if (appliedF3.value != null && appliedF3.value!.isNotEmpty) return true;

    // Check dynamic filters
    for (String? value in appliedFilters.value.values) {
      if (value != null && value.isNotEmpty) return true;
    }

    return false;
  }

  // Build filter results banner widget
  Widget _buildFilterResultsBanner(
    BuildContext context,
    ValueNotifier<String?> appliedF1,
    ValueNotifier<String?> appliedF2,
    ValueNotifier<String?> appliedF3,
    ValueNotifier<Map<String, String?>> appliedFilters,
    ValueNotifier<bool> showTooltip,
    ValueNotifier<String?> selectedF1,
    ValueNotifier<String?> selectedF2,
    ValueNotifier<String?> selectedF3,
    ValueNotifier<Map<String, String?>> selectedFilters,
  ) {
    // Build the filter text using column names instead of values
    List<String> filterTexts = [];

    // Add legacy filter labels (column names)
    if (appliedF1.value != null && appliedF1.value!.isNotEmpty && filter1Label != null) {
      filterTexts.add(filter1Label!);
    }
    if (appliedF2.value != null && appliedF2.value!.isNotEmpty && filter2Label != null) {
      filterTexts.add(filter2Label!);
    }
    if (appliedF3.value != null && appliedF3.value!.isNotEmpty && filter3Label != null) {
      filterTexts.add(filter3Label!);
    }

    // Add dynamic filter column names
    for (MapEntry<String, String?> entry in appliedFilters.value.entries) {
      if (entry.value != null && entry.value!.isNotEmpty) {
        filterTexts.add(entry.key); // Use column name instead of value
      }
    }

    final String filterText = filterTexts.join(', ');

    return Container(
      height: 25,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: AppTheme.filterResultBannerColor,
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: AppTheme.filterResultBannerBorderColor,
          width: 0.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          RichText(
            text: TextSpan(
              text: filterResultBannerText,
              style: AppFonts.regularTextStyle(11, color: AppTheme.black),
              children: [
                TextSpan(
                  text: filterText,
                  style: AppFonts.semiBoldTextStyle(11, color: AppTheme.black),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 10,
            child: IconButton(
              padding: EdgeInsets.zero,
              alignment: Alignment.center,
              icon: const Icon(Icons.close, size: 16),
              style: IconButton.styleFrom(backgroundColor: Colors.transparent),
              onPressed: () {
                // Clear all applied filters
                appliedF1.value = null;
                appliedF2.value = null;
                appliedF3.value = null;
                appliedFilters.value = {};

                // Clear all selected filters (reset dropdown values)
                selectedF1.value = null;
                selectedF2.value = null;
                selectedF3.value = null;
                selectedFilters.value = {};

                // Hide the banner
                showTooltip.value = false;
              },
            ),
          ),
        ],
      ),
    );
  }
}